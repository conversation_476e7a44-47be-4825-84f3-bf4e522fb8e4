import { Injectable, signal, computed, effect } from '@angular/core';
import { useStream } from '@langchain/langgraph-sdk/react';
import { Message, ProcessedEvent, ThreadState, LangGraphEvent, ResearchConfig } from '../models/message.model';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LangGraphService {
  // Signals for reactive state management
  private _messages = signal<Message[]>([]);
  private _isLoading = signal<boolean>(false);
  private _processedEventsTimeline = signal<ProcessedEvent[]>([]);
  private _historicalActivities = signal<Record<string, ProcessedEvent[]>>({});
  private _hasFinalizeEventOccurred = signal<boolean>(false);

  // Public readonly signals
  readonly messages = this._messages.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  readonly processedEventsTimeline = this._processedEventsTimeline.asReadonly();
  readonly historicalActivities = this._historicalActivities.asReadonly();

  // Computed signals
  readonly hasMessages = computed(() => this.messages().length > 0);
  readonly lastMessage = computed(() => {
    const msgs = this.messages();
    return msgs.length > 0 ? msgs[msgs.length - 1] : null;
  });

  private apiUrl = 'http://localhost:2024'; // Will be configurable

  constructor() {
    // Effect to handle finalization and store historical activities
    effect(() => {
      if (this._hasFinalizeEventOccurred() && 
          !this.isLoading() && 
          this.messages().length > 0) {
        
        const lastMsg = this.lastMessage();
        if (lastMsg && lastMsg.type === 'ai' && lastMsg.id) {
          this._historicalActivities.update(prev => ({
            ...prev,
            [lastMsg.id!]: [...this.processedEventsTimeline()]
          }));
        }
        this._hasFinalizeEventOccurred.set(false);
      }
    });
  }

  private processEvent(event: LangGraphEvent): ProcessedEvent | null {
    if (event.generate_query) {
      return {
        title: 'Generating Search Queries',
        data: event.generate_query.query_list.join(', '),
        timestamp: new Date()
      };
    } else if (event.web_research) {
      const sources = event.web_research.sources_gathered || [];
      const numSources = sources.length;
      const uniqueLabels = [...new Set(sources.map(s => s.label).filter(Boolean))];
      const exampleLabels = uniqueLabels.slice(0, 3).join(', ');
      return {
        title: 'Web Research',
        data: `Gathered ${numSources} sources. Related to: ${exampleLabels || 'N/A'}.`,
        timestamp: new Date()
      };
    } else if (event.reflection) {
      return {
        title: 'Reflection',
        data: event.reflection.is_sufficient
          ? 'Search successful, generating final answer.'
          : `Need more information, searching for ${event.reflection.follow_up_queries.join(', ')}`,
        timestamp: new Date()
      };
    } else if (event.finalize_answer) {
      this._hasFinalizeEventOccurred.set(true);
      return {
        title: 'Finalizing Answer',
        data: 'Composing and presenting the final answer.',
        timestamp: new Date()
      };
    }
    return null;
  }

  private getResearchParams(effort: string): { initial_search_query_count: number; max_research_loops: number } {
    switch (effort) {
      case 'low':
        return { initial_search_query_count: 1, max_research_loops: 1 };
      case 'medium':
        return { initial_search_query_count: 3, max_research_loops: 3 };
      case 'high':
        return { initial_search_query_count: 5, max_research_loops: 10 };
      default:
        return { initial_search_query_count: 3, max_research_loops: 3 };
    }
  }

  submitQuery(inputValue: string, config: ResearchConfig): void {
    if (!inputValue.trim()) return;

    // Reset timeline for new query
    this._processedEventsTimeline.set([]);
    this._hasFinalizeEventOccurred.set(false);

    const params = this.getResearchParams(config.effort);
    
    const newMessage: Message = {
      type: 'human',
      content: inputValue,
      id: Date.now().toString(),
      timestamp: new Date()
    };

    // Add human message
    this._messages.update(prev => [...prev, newMessage]);
    this._isLoading.set(true);

    // Simulate LangGraph integration (will be replaced with actual SDK integration)
    this.simulateLangGraphStream(inputValue, params, config.model);
  }

  private simulateLangGraphStream(query: string, params: any, model: string): void {
    // This is a simulation - in real implementation, we'll use the LangGraph SDK
    // For now, let's simulate the process
    
    setTimeout(() => {
      // Simulate generate_query event
      const generateEvent = this.processEvent({
        generate_query: { query_list: [`Research about: ${query}`] }
      });
      if (generateEvent) {
        this._processedEventsTimeline.update(prev => [...prev, generateEvent]);
      }
    }, 500);

    setTimeout(() => {
      // Simulate web_research event
      const researchEvent = this.processEvent({
        web_research: { 
          sources_gathered: [
            { label: 'Wikipedia', url: 'https://wikipedia.org' },
            { label: 'News Source', url: 'https://news.com' }
          ]
        }
      });
      if (researchEvent) {
        this._processedEventsTimeline.update(prev => [...prev, researchEvent]);
      }
    }, 1500);

    setTimeout(() => {
      // Simulate reflection event
      const reflectionEvent = this.processEvent({
        reflection: { is_sufficient: true, follow_up_queries: [] }
      });
      if (reflectionEvent) {
        this._processedEventsTimeline.update(prev => [...prev, reflectionEvent]);
      }
    }, 2500);

    setTimeout(() => {
      // Simulate finalize_answer event
      const finalizeEvent = this.processEvent({
        finalize_answer: { final_answer: 'Generated response' }
      });
      if (finalizeEvent) {
        this._processedEventsTimeline.update(prev => [...prev, finalizeEvent]);
      }

      // Add AI response
      const aiMessage: Message = {
        type: 'ai',
        content: `Here's a comprehensive response to your query: "${query}". This is a simulated response that demonstrates the Angular v20 zoneless architecture with signals-based reactivity.`,
        id: (Date.now() + 1).toString(),
        timestamp: new Date()
      };

      this._messages.update(prev => [...prev, aiMessage]);
      this._isLoading.set(false);
    }, 3500);
  }

  stopProcessing(): void {
    this._isLoading.set(false);
    // In real implementation, this would stop the LangGraph stream
  }

  resetChat(): void {
    this._messages.set([]);
    this._processedEventsTimeline.set([]);
    this._historicalActivities.set({});
    this._hasFinalizeEventOccurred.set(false);
    this._isLoading.set(false);
  }
}
