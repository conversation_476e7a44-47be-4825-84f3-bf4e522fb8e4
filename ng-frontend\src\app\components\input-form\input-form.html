<form (ngSubmit)="onSubmit()" class="input-form">
  <!-- Main input area -->
  <div class="input-container">
    <mat-form-field appearance="outline" class="input-field">
      <textarea
        matInput
        [(ngModel)]="inputValue"
        name="query"
        placeholder="Who won the Euro 2024 and scored the most goals?"
        (keydown)="onKeyDown($event)"
        rows="3"
        class="query-textarea">
      </textarea>
    </mat-form-field>

    <div class="submit-button-container">
      @if (isLoading()) {
        <button
          mat-icon-button
          type="button"
          color="warn"
          (click)="onCancel()"
          class="action-button">
          <mat-icon>stop_circle</mat-icon>
        </button>
      } @else {
        <button
          mat-raised-button
          type="submit"
          color="primary"
          [disabled]="isSubmitDisabled()"
          class="submit-button">
          <mat-icon>search</mat-icon>
          Search
        </button>
      }
    </div>
  </div>

  <!-- Configuration controls -->
  <div class="config-container">
    <div class="config-controls">
      <!-- Effort selection -->
      <mat-form-field appearance="outline" class="config-field">
        <mat-label>
          <mat-icon>psychology</mat-icon>
          Effort
        </mat-label>
        <mat-select [(ngModel)]="effort" name="effort">
          @for (option of effortOptions; track option.value) {
            <mat-option [value]="option.value">
              {{ option.label }}
            </mat-option>
          }
        </mat-select>
      </mat-form-field>

      <!-- Model selection -->
      <mat-form-field appearance="outline" class="config-field">
        <mat-label>
          <mat-icon>memory</mat-icon>
          Model
        </mat-label>
        <mat-select [(ngModel)]="model" name="model">
          @for (option of modelOptions; track option.value) {
            <mat-option [value]="option.value">
              <mat-icon>{{ option.icon }}</mat-icon>
              {{ option.label }}
            </mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div>

    <!-- New Search button (only shown when there's history) -->
    @if (hasHistory()) {
      <button
        mat-stroked-button
        type="button"
        (click)="onNewSearch()"
        class="new-search-button">
        <mat-icon>edit_square</mat-icon>
        New Search
      </button>
    }
  </div>
</form>
