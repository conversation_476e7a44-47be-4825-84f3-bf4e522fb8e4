import { Component, input, output, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { ResearchConfig } from '../../models/message.model';

@Component({
  selector: 'app-input-form',
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule
  ],
  templateUrl: './input-form.html',
  styleUrl: './input-form.scss'
})
export class InputForm {
  // Signal inputs (Angular v20 feature)
  isLoading = input<boolean>(false);
  hasHistory = input<boolean>(false);

  // Signal outputs
  submitQuery = output<{ inputValue: string; config: ResearchConfig }>();
  cancel = output<void>();
  newSearch = output<void>();

  // Internal signals
  inputValue = signal<string>('');
  effort = signal<'low' | 'medium' | 'high'>('medium');
  model = signal<string>('gemini-2.5-flash-preview-04-17');

  // Computed signals
  isSubmitDisabled = computed(() => !this.inputValue().trim() || this.isLoading());

  // Model options
  readonly modelOptions = [
    { value: 'gemini-2.0-flash', label: '2.0 Flash', icon: 'flash_on' },
    { value: 'gemini-2.5-flash-preview-04-17', label: '2.5 Flash', icon: 'flash_on' },
    { value: 'gemini-2.5-pro-preview-05-06', label: '2.5 Pro', icon: 'psychology' }
  ];

  readonly effortOptions = [
    { value: 'low' as const, label: 'Low' },
    { value: 'medium' as const, label: 'Medium' },
    { value: 'high' as const, label: 'High' }
  ];

  onSubmit(): void {
    if (this.isSubmitDisabled()) return;

    const config: ResearchConfig = {
      effort: this.effort(),
      model: this.model(),
      initial_search_query_count: 0, // Will be set by service
      max_research_loops: 0 // Will be set by service
    };

    this.submitQuery.emit({
      inputValue: this.inputValue(),
      config
    });

    this.inputValue.set('');
  }

  onCancel(): void {
    this.cancel.emit();
  }

  onNewSearch(): void {
    this.newSearch.emit();
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.onSubmit();
    }
  }
}
