export interface Message {
  id?: string;
  type: 'human' | 'ai';
  content: string;
  timestamp?: Date;
}

export interface ProcessedEvent {
  title: string;
  data: string;
  timestamp?: Date;
}

export interface ResearchConfig {
  effort: 'low' | 'medium' | 'high';
  model: string;
  initial_search_query_count: number;
  max_research_loops: number;
}

export interface ThreadState {
  messages: Message[];
  initial_search_query_count: number;
  max_research_loops: number;
  reasoning_model: string;
}

export interface LangGraphEvent {
  generate_query?: {
    query_list: string[];
  };
  web_research?: {
    sources_gathered: Array<{
      label: string;
      url: string;
    }>;
  };
  reflection?: {
    is_sufficient: boolean;
    follow_up_queries: string[];
  };
  finalize_answer?: {
    final_answer: string;
  };
}
